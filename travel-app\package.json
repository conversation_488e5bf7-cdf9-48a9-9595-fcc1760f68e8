{"name": "travel-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/poppins": "^5.2.6", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@tailwindcss/postcss": "^4.1.7", "animate.css": "^4.1.1", "autoprefixer": "^10.4.21", "gsap": "^3.13.0", "lottie-react": "^2.4.1", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^4.1.2", "react-tsparticles": "^2.12.2", "tailwindcss": "^3.4.4", "three": "^0.176.0", "tsparticles": "^3.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}