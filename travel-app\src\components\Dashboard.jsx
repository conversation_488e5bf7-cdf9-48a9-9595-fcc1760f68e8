import { useState, useEffect } from 'react';

// Mock data for trips
const mockTrips = [
  {
    id: 1,
    title: "Bali Adventure",
    destination: "Bali, Indonesia",
    duration: "7 days",
    price: "$1,200",
    image: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1",
    spots: 3,
    date: "March 15-22, 2025",
    organizer: "<PERSON> Chen",
    tags: ["Beach", "Culture", "Adventure"]
  },
  {
    id: 2,
    title: "Tokyo Explorer",
    destination: "Tokyo, Japan",
    duration: "5 days",
    price: "$1,800",
    image: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf",
    spots: 2,
    date: "April 10-15, 2025",
    organizer: "<PERSON>",
    tags: ["City", "Food", "Culture"]
  },
  {
    id: 3,
    title: "Swiss Alps Trek",
    destination: "Switzerland",
    duration: "10 days",
    price: "$2,500",
    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
    spots: 4,
    date: "May 20-30, 2025",
    organizer: "<PERSON>",
    tags: ["Mountains", "Hiking", "Nature"]
  }
];

const completedTrips = [
  {
    id: 1,
    title: "Iceland Northern Lights",
    destination: "Reykjavik, Iceland",
    image: "https://images.unsplash.com/photo-1531366936337-7c912a4589a7",
    rating: 4.9,
    participants: 8,
    date: "December 2024"
  },
  {
    id: 2,
    title: "Santorini Sunset",
    destination: "Santorini, Greece",
    image: "https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff",
    rating: 4.8,
    participants: 6,
    date: "October 2024"
  }
];

const testimonials = [
  {
    id: 1,
    name: "Alex Rodriguez",
    trip: "Bali Adventure",
    rating: 5,
    comment: "Amazing experience! Met incredible people and saw breathtaking places.",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d"
  },
  {
    id: 2,
    name: "Lisa Park",
    trip: "Tokyo Explorer",
    rating: 5,
    comment: "Perfect organization and wonderful travel companions. Highly recommend!",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786"
  }
];

const popularDestinations = [
  { name: "Paris, France", visits: "2.3k", image: "https://images.unsplash.com/photo-1502602898536-47ad22581b52" },
  { name: "New York, USA", visits: "1.8k", image: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9" },
  { name: "Dubai, UAE", visits: "1.5k", image: "https://images.unsplash.com/photo-1512453979798-5ea266f8880c" },
  { name: "London, UK", visits: "1.2k", image: "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad" }
];

export default function Dashboard({ onLogout }) {
  const [currentTripIndex, setCurrentTripIndex] = useState(0);
  const [currentCompletedIndex, setCurrentCompletedIndex] = useState(0);
  const [showPostTrip, setShowPostTrip] = useState(false);

  // Auto-rotate carousels
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTripIndex((prev) => (prev + 1) % mockTrips.length);
      setCurrentCompletedIndex((prev) => (prev + 1) % completedTrips.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#EC8E3D] via-[#FCCB6E] to-[#6F93AD]">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">NomadNova</h1>
              <nav className="hidden md:flex space-x-6">
                <a href="#trips" className="text-white/80 hover:text-white transition-colors">Trips</a>
                <a href="#completed" className="text-white/80 hover:text-white transition-colors">Completed</a>
                <a href="#destinations" className="text-white/80 hover:text-white transition-colors">Destinations</a>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowPostTrip(true)}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-full transition-colors"
              >
                Post Trip
              </button>
              <button
                onClick={onLogout}
                className="bg-red-500/20 hover:bg-red-500/30 text-white px-4 py-2 rounded-full transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">
        {/* Welcome Section */}
        <section className="text-center text-white">
          <h2 className="text-4xl font-bold mb-4">Welcome back, Traveler!</h2>
          <p className="text-xl text-white/80">Discover your next adventure with like-minded explorers</p>
        </section>

        {/* Available Trips Carousel */}
        <section id="trips" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-3xl font-bold text-white">Available Trips</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentTripIndex((prev) => (prev - 1 + mockTrips.length) % mockTrips.length)}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-full text-white transition-colors"
              >
                ←
              </button>
              <button
                onClick={() => setCurrentTripIndex((prev) => (prev + 1) % mockTrips.length)}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-full text-white transition-colors"
              >
                →
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockTrips.map((trip, index) => (
              <div
                key={trip.id}
                className={`bg-white/10 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 transition-all duration-500 ${
                  index === currentTripIndex ? 'scale-105 shadow-2xl' : 'scale-100'
                }`}
              >
                <img
                  src={trip.image}
                  alt={trip.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="text-xl font-bold text-white">{trip.title}</h4>
                    <span className="text-2xl font-bold text-[#FCCB6E]">{trip.price}</span>
                  </div>
                  <p className="text-white/80 mb-2">{trip.destination}</p>
                  <p className="text-white/60 mb-3">{trip.duration} • {trip.date}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {trip.tags.map((tag) => (
                      <span key={tag} className="px-3 py-1 bg-white/20 rounded-full text-sm text-white">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/80">{trip.spots} spots left</span>
                    <button className="bg-[#EC8E3D] hover:bg-[#FCCB6E] text-white px-6 py-2 rounded-full transition-colors">
                      Join Trip
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Post Trip Section with AI Suggestions */}
        {showPostTrip && (
          <section className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 max-w-2xl w-full border border-white/20">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">Post a New Trip</h3>
                <button
                  onClick={() => setShowPostTrip(false)}
                  className="text-white/60 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-white mb-2">Destination</label>
                  <input
                    type="text"
                    placeholder="Where are you going?"
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-[#EC8E3D]"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white mb-2">Duration</label>
                    <input
                      type="text"
                      placeholder="e.g., 7 days"
                      className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-[#EC8E3D]"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">Budget</label>
                    <input
                      type="text"
                      placeholder="e.g., $1,500"
                      className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-[#EC8E3D]"
                    />
                  </div>
                </div>

                <div className="bg-[#EC8E3D]/20 border border-[#EC8E3D]/30 rounded-xl p-4">
                  <h4 className="text-white font-semibold mb-2">🤖 AI Suggestions</h4>
                  <p className="text-white/80 text-sm mb-3">Based on your destination, here are some recommendations:</p>
                  <ul className="text-white/70 text-sm space-y-1">
                    <li>• Best time to visit: March-May for pleasant weather</li>
                    <li>• Recommended activities: Temple tours, beach hopping, local cooking classes</li>
                    <li>• Budget tip: Consider staying in local guesthouses to save 40%</li>
                    <li>• Travel tip: Book flights 2-3 months in advance for better deals</li>
                  </ul>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={() => setShowPostTrip(false)}
                    className="flex-1 bg-white/20 hover:bg-white/30 text-white py-3 rounded-xl transition-colors"
                  >
                    Cancel
                  </button>
                  <button className="flex-1 bg-[#EC8E3D] hover:bg-[#FCCB6E] text-white py-3 rounded-xl transition-colors">
                    Post Trip
                  </button>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Completed Trips Carousel */}
        <section id="completed" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-3xl font-bold text-white">Completed Adventures</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentCompletedIndex((prev) => (prev - 1 + completedTrips.length) % completedTrips.length)}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-full text-white transition-colors"
              >
                ←
              </button>
              <button
                onClick={() => setCurrentCompletedIndex((prev) => (prev + 1) % completedTrips.length)}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-full text-white transition-colors"
              >
                →
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {completedTrips.map((trip, index) => (
              <div
                key={trip.id}
                className={`bg-white/10 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 transition-all duration-500 ${
                  index === currentCompletedIndex ? 'scale-105 shadow-2xl' : 'scale-100'
                }`}
              >
                <img
                  src={trip.image}
                  alt={trip.title}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h4 className="text-xl font-bold text-white mb-2">{trip.title}</h4>
                  <p className="text-white/80 mb-3">{trip.destination}</p>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-400">★</span>
                      <span className="text-white">{trip.rating}</span>
                      <span className="text-white/60">({trip.participants} travelers)</span>
                    </div>
                    <span className="text-white/60">{trip.date}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Photos and Videos Section */}
        <section className="space-y-6">
          <h3 className="text-3xl font-bold text-white">Travel Memories</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
              "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
              "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf",
              "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1",
              "https://images.unsplash.com/photo-1531366936337-7c912a4589a7",
              "https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff",
              "https://images.unsplash.com/photo-1502602898536-47ad22581b52",
              "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9"
            ].map((image, index) => (
              <div
                key={index}
                className="relative group cursor-pointer overflow-hidden rounded-xl"
              >
                <img
                  src={image}
                  alt={`Travel memory ${index + 1}`}
                  className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="space-y-6">
          <h3 className="text-3xl font-bold text-white">What Travelers Say</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
              >
                <div className="flex items-center space-x-4 mb-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="text-white font-semibold">{testimonial.name}</h4>
                    <p className="text-white/60 text-sm">{testimonial.trip}</p>
                  </div>
                  <div className="ml-auto flex">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <span key={i} className="text-yellow-400">★</span>
                    ))}
                  </div>
                </div>
                <p className="text-white/80 italic">"{testimonial.comment}"</p>
              </div>
            ))}
          </div>
        </section>

        {/* Popular Destinations Section */}
        <section id="destinations" className="space-y-6">
          <h3 className="text-3xl font-bold text-white">Popular Destinations</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {popularDestinations.map((destination, index) => (
              <div
                key={index}
                className="relative group cursor-pointer overflow-hidden rounded-xl bg-white/10 backdrop-blur-md border border-white/20 hover:scale-105 transition-transform duration-300"
              >
                <img
                  src={destination.image}
                  alt={destination.name}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <h4 className="text-white font-semibold text-sm mb-1">{destination.name}</h4>
                  <p className="text-white/80 text-xs">{destination.visits} visits</p>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}